#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script pyRevit para conversão de arquivos RFA (Revit Family) para formato OBJ
Desenvolvido para o projeto BIMEX - removendo dependência do ODA BIMRV

Uso:
    python export_obj.py --input arquivo.rfa --output arquivo.obj

Requisitos:
    - pyRevit instalado
    - Revit instalado no sistema
    - Acesso às APIs do Revit
"""

import sys
import os
import argparse
import tempfile
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    # Importar APIs do Revit via pyRevit
    import clr
    clr.AddReference('RevitAPI')
    clr.AddReference('RevitAPIUI')
    
    from Autodesk.Revit.DB import *
    from Autodesk.Revit.ApplicationServices import Application
    from Autodesk.Revit.DB.ExportUtils import ExportToOBJ
    
    # Importar utilitários do pyRevit
    from pyrevit import revit, DB, UI
    from pyrevit.framework import List
    
    REVIT_AVAILABLE = True
    logger.info("✅ APIs do Revit carregadas com sucesso")
    
except ImportError as e:
    REVIT_AVAILABLE = False
    logger.error(f"❌ Erro ao importar APIs do Revit: {e}")
    logger.error("Certifique-se de que o pyRevit e o Revit estão instalados")


class RevitOBJExporter:
    """Classe para exportar famílias do Revit para formato OBJ"""
    
    def __init__(self):
        self.app = None
        self.doc = None
        
    def initialize_revit(self):
        """Inicializa a aplicação do Revit"""
        try:
            if not REVIT_AVAILABLE:
                raise Exception("APIs do Revit não estão disponíveis")
                
            # Tentar obter aplicação ativa do Revit
            try:
                self.app = revit.app
                logger.info("✅ Conectado à instância ativa do Revit")
            except:
                # Se não houver instância ativa, tentar criar uma nova
                logger.info("🔄 Criando nova instância do Revit...")
                self.app = Application()
                logger.info("✅ Nova instância do Revit criada")
                
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Revit: {e}")
            return False
    
    def load_family_file(self, rfa_path):
        """Carrega arquivo de família RFA"""
        try:
            if not os.path.exists(rfa_path):
                raise FileNotFoundError(f"Arquivo não encontrado: {rfa_path}")
            
            logger.info(f"📂 Carregando família: {rfa_path}")
            
            # Abrir documento de família
            self.doc = self.app.OpenDocumentFile(rfa_path)
            
            if self.doc is None:
                raise Exception("Falha ao abrir documento de família")
                
            logger.info("✅ Família carregada com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao carregar família: {e}")
            return False
    
    def export_to_obj(self, output_path):
        """Exporta a família para formato OBJ"""
        try:
            if self.doc is None:
                raise Exception("Nenhum documento carregado")
            
            logger.info(f"🔄 Exportando para OBJ: {output_path}")
            
            # Criar diretório de saída se não existir
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # Configurar opções de exportação OBJ
            options = OBJExportOptions()
            options.ExportFileType = OBJFileType.OBJ
            
            # Obter todos os elementos 3D da família
            collector = FilteredElementCollector(self.doc)
            elements = collector.WhereElementIsNotElementType().ToElements()
            
            # Filtrar apenas elementos com geometria 3D
            geometric_elements = []
            for element in elements:
                if element.get_Geometry(Options()) is not None:
                    geometric_elements.append(element)
            
            if not geometric_elements:
                logger.warning("⚠️ Nenhum elemento geométrico encontrado na família")
                # Criar arquivo OBJ vazio para evitar erro
                with open(output_path, 'w') as f:
                    f.write("# Arquivo OBJ vazio - família sem geometria 3D\n")
                return True
            
            # Converter lista para formato .NET
            element_ids = List[ElementId]()
            for element in geometric_elements:
                element_ids.Add(element.Id)
            
            # Exportar para OBJ
            result = self.doc.Export(
                os.path.dirname(output_path),
                os.path.splitext(os.path.basename(output_path))[0],
                element_ids,
                options
            )
            
            if result:
                logger.info("✅ Exportação OBJ concluída com sucesso")
                return True
            else:
                logger.error("❌ Falha na exportação OBJ")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro durante exportação: {e}")
            # Tentar método alternativo usando geometria direta
            return self._export_geometry_direct(output_path)
    
    def _export_geometry_direct(self, output_path):
        """Método alternativo para exportar geometria diretamente"""
        try:
            logger.info("🔄 Tentando exportação direta da geometria...")
            
            vertices = []
            faces = []
            vertex_count = 0
            
            # Obter geometria de todos os elementos
            collector = FilteredElementCollector(self.doc)
            elements = collector.WhereElementIsNotElementType().ToElements()
            
            for element in elements:
                geometry = element.get_Geometry(Options())
                if geometry is not None:
                    for geom_obj in geometry:
                        if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                            # Processar faces do sólido
                            for face in geom_obj.Faces:
                                mesh = face.Triangulate()
                                if mesh is not None:
                                    # Adicionar vértices
                                    for i in range(mesh.NumTriangles):
                                        triangle = mesh.get_Triangle(i)
                                        for j in range(3):
                                            vertex = triangle.get_Vertex(j)
                                            vertices.append(f"v {vertex.X:.6f} {vertex.Y:.6f} {vertex.Z:.6f}")
                                    
                                    # Adicionar faces (triângulos)
                                    for i in range(mesh.NumTriangles):
                                        face_indices = [
                                            vertex_count + (i * 3) + 1,
                                            vertex_count + (i * 3) + 2,
                                            vertex_count + (i * 3) + 3
                                        ]
                                        faces.append(f"f {face_indices[0]} {face_indices[1]} {face_indices[2]}")
                                    
                                    vertex_count += mesh.NumTriangles * 3
            
            # Escrever arquivo OBJ
            with open(output_path, 'w') as f:
                f.write("# Arquivo OBJ exportado pelo BIMEX pyRevit Converter\n")
                f.write(f"# Família: {self.doc.Title}\n")
                f.write(f"# Vértices: {len(vertices)}\n")
                f.write(f"# Faces: {len(faces)}\n\n")
                
                # Escrever vértices
                for vertex in vertices:
                    f.write(vertex + "\n")
                
                f.write("\n")
                
                # Escrever faces
                for face in faces:
                    f.write(face + "\n")
            
            logger.info(f"✅ Exportação direta concluída: {len(vertices)} vértices, {len(faces)} faces")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na exportação direta: {e}")
            return False
    
    def close_document(self):
        """Fecha o documento sem salvar"""
        try:
            if self.doc is not None:
                self.doc.Close(False)  # False = não salvar
                logger.info("📄 Documento fechado")
        except Exception as e:
            logger.warning(f"⚠️ Erro ao fechar documento: {e}")


def main():
    """Função principal"""
    parser = argparse.ArgumentParser(
        description="Converte arquivos RFA do Revit para formato OBJ usando pyRevit"
    )
    parser.add_argument(
        '--input', '-i',
        required=True,
        help="Caminho para o arquivo RFA de entrada"
    )
    parser.add_argument(
        '--output', '-o',
        required=True,
        help="Caminho para o arquivo OBJ de saída"
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help="Modo verboso (mais detalhes no log)"
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Verificar se o arquivo de entrada existe
    if not os.path.exists(args.input):
        logger.error(f"❌ Arquivo de entrada não encontrado: {args.input}")
        return 1
    
    # Verificar se as APIs do Revit estão disponíveis
    if not REVIT_AVAILABLE:
        logger.error("❌ APIs do Revit não estão disponíveis")
        logger.error("Certifique-se de que o pyRevit e o Revit estão instalados")
        return 1
    
    # Executar conversão
    exporter = RevitOBJExporter()
    
    try:
        # Inicializar Revit
        if not exporter.initialize_revit():
            return 1
        
        # Carregar família
        if not exporter.load_family_file(args.input):
            return 1
        
        # Exportar para OBJ
        if not exporter.export_to_obj(args.output):
            return 1
        
        logger.info("🎉 Conversão concluída com sucesso!")
        return 0
        
    except Exception as e:
        logger.error(f"❌ Erro durante conversão: {e}")
        return 1
        
    finally:
        # Limpar recursos
        exporter.close_document()


if __name__ == "__main__":
    sys.exit(main())
